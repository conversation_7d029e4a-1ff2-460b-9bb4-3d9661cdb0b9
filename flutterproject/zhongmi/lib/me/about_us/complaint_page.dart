import 'package:flutter/material.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';

/// 责任说明及投诉建议页面
/// 根据设计稿实现的投诉反馈页面，包含说明文字、输入框、图片上传和提交功能
class ComplaintPage extends StatefulWidget {
  const ComplaintPage({super.key});

  @override
  State<ComplaintPage> createState() => _ComplaintPageState();
}

class _ComplaintPageState extends State<ComplaintPage> {
  // 文本输入控制器
  final TextEditingController _complaintController = TextEditingController();

  // 图片选择器
  final ImagePicker _picker = ImagePicker();

  // 选中的图片列表
  List<XFile> _selectedImages = [];

  @override
  void dispose() {
    _complaintController.dispose();
    super.dispose();
  }

  /// 选择图片方法
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(image);
        });
      }
    } catch (e) {
      // 处理选择图片错误
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('选择图片失败，请重试')),
      );
    }
  }

  /// 删除选中的图片
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// 提交投诉建议
  void _submitComplaint() {
    if (_complaintController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入您的问题或建议')),
      );
      return;
    }

    // 这里可以添加提交到服务器的逻辑
    // 目前只是显示提交成功的提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('提交成功，我们会尽快处理您的反馈'),
        backgroundColor: Colors.green,
      ),
    );

    // 清空输入内容
    _complaintController.clear();
    setState(() {
      _selectedImages.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Color(0xFF666666),
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '责任说明及投诉建议',
          style: TextStyle(
            fontSize: 17,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 责任说明文字
            _buildResponsibilityText(),

            const SizedBox(height: 30),

            // 问题输入框
            _buildComplaintInput(),

            const SizedBox(height: 20),

            // 图片上传区域
            _buildImageUploadSection(),

            const SizedBox(height: 40),

            // 提交按钮
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }